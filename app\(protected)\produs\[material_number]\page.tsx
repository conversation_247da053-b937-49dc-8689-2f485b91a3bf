"server-only"

import { getFeaturedProducts, getProductByMaterialNumber } from "@/app/getData/products";
import ProductPageContent from "@/app/components/product/ProductPage";
import { notFound, redirect } from "next/navigation";
import { ProductCardInterface } from "@/types/product";
import { getCurrentDbUser } from "@/lib/auth";
import ProductGrid from "@/app/components/product/ProductGrid";
import { getPretSiStoc, getPretSiStocBatch, getPriceFor4th, getPricesFor4thBatch } from "@/lib/mssql/query";

export default async function ProductRoute({ params }: { params: Promise<{ material_number: string }> }) {
   const { material_number } = await params;

  // 1️⃣ Fetch user, main product, featured in parallel
  const [user, product, featured] = await Promise.all([
    getCurrentDbUser(),
    getProductByMaterialNumber(material_number),
    getFeaturedProducts(),
  ]);

  if (!user) {
    redirect("/sign-in");
  }
  if (!product) {
    return notFound();
  }

  // 2️⃣ Determine if user gets special 4️⃣-level pricing
  const has4th =
    user.role.includes("fourLvlAdminAB") ||
    user.role.includes("fourLvlInregistratAB");

  // 3️⃣ Kick off all remaining I/O in parallel:
  const featuredSkus = featured.map((p) => p.Material_Number);
  const [
    mainStockArr,              // PretSiStocAM[]
    main4thPrice,              // GetPrice4Lvl | null
    featuredStockMap,          // Record<string, PretSiStocAM[]>
    featured4thPriceBatch,     // GetPrice4LvlBatch[]
  ] = await Promise.all([
    getPretSiStoc(material_number),
    has4th
      ? getPriceFor4th(material_number, user.userAM || "")
      : Promise.resolve(null),
    getPretSiStocBatch(featuredSkus),
    has4th
      ? getPricesFor4thBatch(featuredSkus, user.userAM || "")
      : Promise.resolve([]),
  ]);

  // 4️⃣ Compute price
  const mainDisplayPrice =
    main4thPrice?.pret ?? product.FinalPrice;

  // 5️⃣ Merge featured products
  const price4Map = new Map<string, number>(
    featured4thPriceBatch.map((p) => [p.itemno, p.pret])
  );

  const productsWithData: ProductCardInterface[] = featured.map((p) => {
    const batch = featuredStockMap[p.Material_Number] ?? [];
    const stock = batch.reduce((sum, x) => sum + x.stoc, 0);

    // only look up a 4th-level price if allowed
    const override = has4th
      ? price4Map.get(p.Material_Number)
      : undefined;

    return {
      ...p,
      stock,
      // fallback to DB price if no override (or user not allowed)
      displayPrice: override ?? p.FinalPrice,
    };
  });

  // 6️⃣ Finally render
  return (
    <>
      <ProductPageContent
        product={product}
        mainStockArr={mainStockArr}
        displayPrice={mainDisplayPrice}
      />

      {productsWithData.length > 0 && (
        <ProductGrid
          products={productsWithData}
          title="Produse recomandate"
          description="Ar putea să-ți placă și…"
        />
      )}
    </>
  );
}



//initial
// export default async function ProductRoute({ params }: { params: Promise<{ material_number: string }> }) {
//   const { material_number } = await params;

//   // 1️⃣ Fetch user, product, and featured list in parallel
//   const [user, product, featured] = await Promise.all([
//     getCurrentDbUser(),
//     getProductByMaterialNumber(material_number),
//     getFeaturedProducts(),
//   ]);

//   if (!user) {
//     return redirect("/sign-in");
//   }
//   if (!product) {
//     return notFound();
//   }

//   // 2️⃣ Batch stock+price lookups for *all* SKUs (main + featured)
//   const allSkus = [material_number, ...featured.map((p) => p.Material_Number)];
//   const pretSiStocMap = await getPretSiStocBatch(allSkus);

//   // 3️⃣ Get wishlist codes
//   const wishlistCodes = await getWishlistProductCodes(user.id);

//   // 4️⃣ Build “featured with data”
//   const productsWithData = featured.map((p) => {
//     const batch = pretSiStocMap[p.Material_Number] ?? [];
//     const totalStock = batch.reduce((sum, x) => sum + x.stoc, 0);
//     const displayPrice = batch[0]?.pret ?? p.FinalPrice;

//     return {
//       ...p,
//       isInWishlist: wishlistCodes.has(p.Material_Number),
//       stock: totalStock,
//       displayPrice,
//     };
//   });

//   // 5️⃣ Derive main product’s stock & displayPrice
//   const mainBatch = pretSiStocMap[material_number] ?? [];
//   const mainStock = mainBatch.reduce((sum, x) => sum + x.stoc, 0);

//   return (
//     <div>
//           <ProductPageContent product={product} stock={mainStock} />

//           {featured.length > 0 && <ProductGrid stock={mainStock} products={productsWithData} title="Produse recomandate" description="Produse care te-ar putea interesa" /> } 
//     </div>
//   );
// }