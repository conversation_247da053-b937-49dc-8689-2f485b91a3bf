import { SignIn } from "@clerk/nextjs";
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs';

//import * as SignIn from '@clerk/elements/sign-in'

export default function SignInPage() {
  return (
    <div className="flex justify-center items-center min-h-[calc(100vh-80px)]">


      <SignedOut>
        {/* Content for signed-out users */}
        <SignIn 
          appearance={{
            elements: {
              formButtonPrimary: 'bg-blue-600 hover:bg-blue-700',
            }
          }}
        />
      </SignedOut>

    </div>
  );
}