"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { sendOrderConfirmationEmail } from "@/lib/emailService";
import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { Cart } from "@/types/cart";
import { OrderDataEmail } from "@/types/email";
import { revalidatePath } from "next/cache";
import { redirect, RedirectType } from "next/navigation";

export async function placeOrder() {

    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[placeOrder] No user authenticated`);
      return { success: false };
    }

    if (!redis) {
      logger.error("[placeOrder] Redis connection not available");
      return { success: false };
    }

    // Retrieve the cart from Redis
    let cart = await redis.get<Cart>(`cart-${user.id}`);

    if (!cart) {
      logger.error(`[placeOrder] No cart found for user ${user.id}`);
      return { success: false };
    }

    if (!cart.items.length) {
      logger.error(`[placeOrder] No items in cart for user ${user.id}`);
      return { success: false };
    }

    let order: { id: string; orderNumber: string } | null = null;

    try {
      order = await prisma.$transaction(async (tx) => {
        const createdOrder = await withRetry(() =>
          tx.order.create({
            data: {
              userId: user.id,
              amount: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
              orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
              notes: cart.order?.notes,
              createdBy: user.id,
            },
            select: {
              id: true,
              orderNumber: true,
            },
          })
        );

        await withRetry(() =>
          tx.orderItem.createMany({
            data: cart.items.map((item) => ({
              orderId: createdOrder.id,
              productId: item.id,
              quantity: item.quantity,
              price: item.FinalPrice ?? 0,
              notes: item.vinNotes,
              notesToInvoice: item.addVinNotesToInvoice,
              vinOrderItem: item.vinNotes,
              createdBy: user.id,
            })),
          })
        );

        return createdOrder; // Return the order from the transaction
      });
    } catch (error) {
      logger.error("Order creation transaction failed:", error);
    }

    if (!order) {
      logger.error(`[placeOrder] No order created for user ${user.id}`);
      return { success: false };
    }

    //send email for confirmation
    const orderData: OrderDataEmail = {
      orderId: order.orderNumber,
      customerName: `${user.firstName} ${user.lastName}`,
      customerEmail: user.email,
      customerId: user.id,
      items: cart.items.map(item => ({
        name: item.Description_Local ?? "",
        code: item.Material_Number,
        quantity: item.quantity,
        price: item.FinalPrice ?? 0,
      })),
      total: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
    };

    const emailResult = await sendOrderConfirmationEmail(orderData);

    if (!emailResult.success) {
      logger.error(`[placeOrder] Email confirmation failed for order ${order.orderNumber}`);
    }

    await redis.del(`cart-${user.id}`);
    await redis.del(`user-orders:${user.id}`);
    revalidatePath("/");
    redirect(`/order-conf?order=${order.orderNumber}`, RedirectType.replace);
}

export async function place4thLevelOrder( formData: FormData ) {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[4thLevelOrder] No user authenticated`);
      return 
    }

    if (!redis) {
      logger.error("[4thLevelOrder] Redis connection not available");
      return 
    }

    // Retrieve the cart from Redis
    let cart = await redis.get<Cart>(`cart-${user.id}`);

    if (!cart) {
      logger.error(`[4thLevelOrder] No cart found for user ${user.id}`);
      return 
    }

    if (!cart.items.length) {
      logger.error(`[4thLevelOrder] No items in cart for user ${user.id}`);
      return
    }

    // Create the order
    const order = await withRetry(() =>
      prisma.order.create({
        data: {
          userId: user.id,
          amount: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
          orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
          notes: cart.order?.notes,
          createdBy: user.id,
        },
      })
    );

    // Create the order items
    await withRetry(() =>
      prisma.orderItem.createMany({
        data: cart.items.map((item) => ({
          orderId: order.id,
          productId: item.id,
          quantity: item.quantity,
          price: item.FinalPrice ?? 0,
          notes: item.vinNotes,
          notesToInvoice: item.addVinNotesToInvoice,
          vinOrderItem: item.vinNotes,
          createdBy: user.id,
        })),
      })
    );

    await redis.del(`cart-${user.id}`);
    await redis.del(`user-orders:${user.id}`);
    revalidatePath("/");
    redirect(`/order-conf?order=${order.orderNumber}`);
  } catch (error) {
    logger.error(`[4thLevelOrder] Error placing order: ${error}`);
    return 
  } finally {
     logger.info(`[4thLevelOrder] Order placed successfully`);
  }
}