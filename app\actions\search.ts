"use server"

import { getSearchSuggestions } from "@/app/getData/search"
import { z } from "zod"

const searchSchema = z.object({
  query: z.string().min(3).max(100)
})

export async function getSearchSuggestionsAction(query: string) {
  try {
    const validatedQuery = searchSchema.parse({ query })
    return await getSearchSuggestions(validatedQuery.query)
  } catch (error) {
    console.error('Error in getSearchSuggestionsAction:', error)
    return { products: [], firstCategory: null }
  }
}
