"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Loader2, CheckCircle, AlertCircle, Bell, Mail, Smartphone, MessageSquare } from "lucide-react";
import { notificationPreferencesSchema, type NotificationPreferencesInput } from "@/lib/zod";
import { updateNotificationPreferences, type ActionResult } from "@/app/actions/account";
import { AccountSettingsData } from "@/app/getData/account-settings";
import { toast } from "sonner";

interface PreferencesFormProps {
  accountData: AccountSettingsData;
}

export default function PreferencesForm({ accountData }: PreferencesFormProps) {
  const [isPending, startTransition] = useTransition();

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { isDirty }
  } = useForm<NotificationPreferencesInput>({
    resolver: zodResolver(notificationPreferencesSchema),
    defaultValues: {
      emailNotifications: accountData.emailNotifications,
      pushNotifications: accountData.pushNotifications,
      smsNotifications: accountData.smsNotifications,
      newsletterOptIn: accountData.newsletterOptIn,
    }
  });

  const watchedValues = watch();

  const onSubmit = (data: NotificationPreferencesInput) => {
    startTransition(async () => {
      try {
        const result = await updateNotificationPreferences(data);
        
        if (result.success) {
          toast.success("Preferintele au fost actualizate cu succes!");
        } else {
          toast.error(result.error);
        }
      } catch (error) {
        toast.error("A aparut o eroare neașteptata. Va rugam sa incercati din nou.");
      }
    });
  };

  const handleSwitchChange = (field: keyof NotificationPreferencesInput, value: boolean) => {
    setValue(field, value, { shouldDirty: true });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Preferinte Notificari
        </CardTitle>
        <CardDescription>
          Gestionati modul in care doriti sa primiti notificari și comunicari
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Notifications */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="emailNotifications" className="text-base font-medium">
                    Notificari prin email
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiti notificari despre comenzi, actualizari de cont și alte informatii importante
                  </p>
                </div>
              </div>
              <Switch
                id="emailNotifications"
                checked={watchedValues.emailNotifications}
                onCheckedChange={(checked) => handleSwitchChange("emailNotifications", checked)}
              />
            </div>

            <Separator />

            {/* Push Notifications */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Smartphone className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="pushNotifications" className="text-base font-medium">
                    Notificari push
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiti notificari instant pe dispozitiv despre activitatea contului
                  </p>
                </div>
              </div>
              <Switch
                id="pushNotifications"
                checked={watchedValues.pushNotifications}
                onCheckedChange={(checked) => handleSwitchChange("pushNotifications", checked)}
              />
            </div>

            <Separator />

            {/* SMS Notifications */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="smsNotifications" className="text-base font-medium">
                    Notificari SMS
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiti SMS-uri pentru confirmari de comenzi și actualizari critice
                  </p>
                </div>
              </div>
              <Switch
                id="smsNotifications"
                checked={watchedValues.smsNotifications}
                onCheckedChange={(checked) => handleSwitchChange("smsNotifications", checked)}
                disabled={!accountData.phoneNumber}
              />
            </div>

            {!accountData.phoneNumber && (
              <p className="text-xs text-muted-foreground ml-8">
                Pentru a activa notificarile SMS, adaugati un numar de telefon in sectiunea Profil
              </p>
            )}

            <Separator />

            {/* Newsletter */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-muted-foreground" />
                <div className="space-y-1">
                  <Label htmlFor="newsletterOptIn" className="text-base font-medium">
                    Newsletter și oferte
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Primiti newsletter-ul nostru cu oferte speciale, noutati și sfaturi utile
                  </p>
                </div>
              </div>
              <Switch
                id="newsletterOptIn"
                checked={watchedValues.newsletterOptIn}
                onCheckedChange={(checked) => handleSwitchChange("newsletterOptIn", checked)}
              />
            </div>
          </div>

          {/* Information Alert */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Nota importanta:</strong> Anumite notificari critice (cum ar fi confirmarile de comenzi și 
              actualizarile de securitate) vor fi trimise indiferent de aceste setari pentru a va proteja contul.
            </AlertDescription>
          </Alert>

          {/* Privacy Notice */}
          <div className="bg-muted/50 p-4 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Confidentialitate și date</h4>
            <p className="text-xs text-muted-foreground">
              Respectam confidentialitatea dvs. și nu vom impartași niciodata informatiile dvs. cu terti 
              fara consimtamântul dvs. explicit. Puteti modifica aceste preferinte oricând. 
              Pentru mai multe informatii, consultati{" "}
              <a href="/privacy" className="text-primary hover:underline">
                Politica de Confidentialitate
              </a>.
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isPending || !isDirty}
              className="bg-[#0066B1] hover:bg-[#004d85] text-white"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se salveaza...
                </>
              ) : (
                "Salveaza preferintele"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
