// components/ProfileImageUpload.tsx
"use client";

import { useState } from "react";
import { useUser } from "@clerk/nextjs";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, Upload, Trash2, Loader2 } from "lucide-react";
import { toast } from "sonner";

export default function ProfileImageUpload() {
  const { user, isLoaded } = useUser();
  const [isUploading, setIsUploading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleImageUpload = async (file: File) => {

    if (!file || !user) return;

    // Validate file
    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error("Imaginea este prea mare. Mărimea maximă este 5MB.");
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast.error("Vă rugăm să selectați un fișier imagine valid.");
      return;
    }

    setIsUploading(true);

    try {
      // Clerk handles the upload automatically
      await user.setProfileImage({ file });
      toast.success("Imaginea de profil a fost actualizată cu succes!");
    } catch (error: any) {
      console.error('Profile image upload error:', error);
      
      let errorMessage = "A apărut o eroare la încărcarea imaginii.";
      if (error.errors?.[0]) {
        const clerkError = error.errors[0];
        switch (clerkError.code) {
          case 'form_param_size_too_large':
            errorMessage = "Imaginea este prea mare.";
            break;
          case 'form_param_format_invalid':
            errorMessage = "Formatul imaginii nu este suportat.";
            break;
          default:
            errorMessage = clerkError.longMessage || errorMessage;
        }
      }
      
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleImageDelete = async () => {
    if (!user) return;

    setIsDeleting(true);

    try {
      await user.setProfileImage({ file: null });
      toast.success("Imaginea de profil a fost ștearsă.");
    } catch (error) {
      console.error('Profile image delete error:', error);
      toast.error("A apărut o eroare la ștergerea imaginii.");
    } finally {
      setIsDeleting(false);
    }
  };

  
  const handleUploadClick = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0];
      if (file) {
        handleImageUpload(file);
      }
    };
    input.click();
  };

  if (!isLoaded) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  const userInitials = `${user?.firstName?.[0] || ''}${user?.lastName?.[0] || ''}`.toUpperCase();
  const hasImage = !!user?.imageUrl;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Imagine de profil
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Image Display */}
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user?.imageUrl} alt="Imagine profil" />
            <AvatarFallback className="text-lg font-semibold bg-blue-100 text-blue-600">
              {userInitials || '?'}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <p className="text-sm text-gray-600 mb-2">
              {hasImage 
                ? "Imaginea dvs. de profil actuală" 
                : "Nu aveți o imagine de profil setată"
              }
            </p>
            <p className="text-xs text-gray-500">
              Formatul recomandat: JPG, PNG sau GIF. Mărimea maximă: 5MB.
            </p>
          </div>
        </div>

        {/* Upload/Delete Actions */}
        <div className="flex gap-2">
          {/* Upload Button */}
          <Button
            type="button"
            variant="outline"
            onClick={handleUploadClick}
            disabled={isUploading || isDeleting}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Se încarcă...
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                {hasImage ? 'Schimbă imaginea' : 'Încarcă imagine'}
              </>
            )}
          </Button>

          {/* Delete Button */}
          {hasImage && (
            <Button
              variant="outline"
              onClick={handleImageDelete}
              disabled={isUploading || isDeleting}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se șterge...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Șterge
                </>
              )}
            </Button>
          )}
        </div>

        {/* Tips */}
        {/* <div className="bg-blue-50 p-3 rounded-lg">
          <p className="text-sm text-blue-800 font-medium mb-1">💡 Sfaturi pentru o imagine bună:</p>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Folosiți o imagine clară cu fața vizibilă</li>
            <li>• Evitați imaginile întunecate sau neclare</li>
            <li>• Imaginea va fi redimensionată automat</li>
          </ul>
        </div> */}
      </CardContent>
    </Card>
  );
}