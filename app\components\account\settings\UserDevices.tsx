// components/UserDevices.jsx
'use client'
import { UserDevice } from '@/app/getData/account-settings';
import { formatDate } from '@/lib/utils';
import { useReverification, useSession, useSessionList, useUser } from '@clerk/nextjs';
import { SessionResource } from '@clerk/types';
import { useState } from 'react';
import { revokeSession } from '@/app/actions/account';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function UserDevices( { devicesProp } : { devicesProp: UserDevice[] }) {
  const [devices, setDevices] = useState(devicesProp);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [deviceToDelete, setDeviceToDelete] = useState<string | null>(null);
  const { session: currentSession } = useSession();
  const router = useRouter();

  const getDeviceIcon = (deviceType : string | undefined, isMobile : boolean | undefined) => {
    if (isMobile) return '📱';
    if (deviceType?.toLowerCase().includes('windows')) return '🖥️';
    if (deviceType?.toLowerCase().includes('mac')) return '💻';
    if (deviceType?.toLowerCase().includes('linux')) return '🐧';
    return '💻';
  };

  const getBrowserIcon = (browserName : string | undefined) => {
    switch (browserName?.toLowerCase()) {
      case 'chrome': return '🌐';
      case 'firefox': return '🦊';
      case 'safari': return '🧭';
      case 'edge': return '🔷';
      default: return '🌐';
    }
  };   
  
  const deleteDevice =  useReverification(async (deviceId: string) => {
    try {
      const response = await revokeSession(deviceId);
      if (!response.success) {
        toast.error("Echipamentul nu a putut fi șters.");
      } else {
        toast.success("Echipamentul a fost șters cu succes.");
      }
    } catch (error) {
      toast.error("Echipamentul nu a putut fi șters.");
    } finally {
      setDeletingId(null);
    }
  });
  
  const handleDeleteDevice = async () => {
    if (!deviceToDelete) return;
    
    const isCurrentDevice = currentSession?.id === deviceToDelete;
    setDeletingId(deviceToDelete);

    try {
      // Call the server action to revoke the session
      await deleteDevice(deviceToDelete);
      
      if (isCurrentDevice) {
        // Redirect to sign-in only if user deleted their current session
        router.push('/sign-in');
      } else {
        // For other devices, just update the UI
        setDevices(prevDevices => prevDevices.filter(device => device.id !== deviceToDelete));
        setDeviceToDelete(null);
        setDeletingId(null);
      }
      
    } catch (error) {
      // Only show error if it's not the current session
      if (!isCurrentDevice) {
        toast.error("Echipamentul nu a putut fi șters.");
      }
      setDeletingId(null);
      setDeviceToDelete(null);
    }
  };

  const isCurrentDevice = (deviceId: string) => currentSession?.id === deviceId;
return (
  <>
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
      {devices.map((device) => {
        const isCurrent = isCurrentDevice(device.id);
        
        return (
          <div 
            key={device.id} 
            className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4 flex-1">
                <div className="text-3xl">
                  {getDeviceIcon(device.deviceType, device.isMobile)}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="font-semibold text-lg text-gray-800">
                      {device.deviceType || 'Unknown Device'}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      device.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {device.status}
                    </span>
                    {isCurrent && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Echipament curent
                      </span>
                    )}
                  </div>
                
                  <div className="space-y-1 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <span>{getBrowserIcon(device.browserName)}</span>
                      <span>
                        {device.browserName} {device.browserVersion}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span>📍</span>
                      <span>
                        {device.city}, {device.country} • {device.ipAddress}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <span>🕒</span>
                      <span>Ultima activitate: {formatDate(new Date(device.lastActiveAt).toString())}</span>
                    </div>
                    
                    {device.isMobile && (
                      <div className="flex items-center space-x-2">
                        <span>📱</span>
                        <span className="text-blue-600 font-medium">Dispozitiv mobil</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col items-end space-y-2">
                <div className="text-xs text-gray-500">
                  Conectat la {formatDate(new Date(device.lastActiveAt).toString())}
                </div>
                <button
                  onClick={() => setDeviceToDelete(device.id)}
                  disabled={deletingId === device.id}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                    isCurrent 
                      ? 'text-blue-600 hover:text-blue-700 hover:bg-blue-50' 
                      : 'text-red-600 hover:text-red-700 hover:bg-red-50'
                  }`}
                >
                  {deletingId === device.id ? 'Se sterge...' : isCurrent ? 'Deconectare' : 'Sterge'}
                </button>
              </div>
            </div>
          </div>
        );
      })}
    </div>

    {/* Confirmation Dialog */}
    <Dialog open={!!deviceToDelete} onOpenChange={(open) => !open && setDeviceToDelete(null)}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            {isCurrentDevice(deviceToDelete || '') ? 'Deconectare?' : 'Sterge Dispozitiv?'}
          </DialogTitle>
          <DialogDescription className="pt-3">
            {isCurrentDevice(deviceToDelete || '') ? (
              <>
                <span className="block mb-2">Aceasta actiune va deconecta acest dispozitiv.</span>
                <span className="block text-sm text-muted-foreground">Va trebui să vă conectați din nou pentru a continua utilizarea acestui dispozitiv.</span>
              </>
            ) : (
              <>
                <span className="block mb-2">Sunteti sigur ca doriti sa stergeti acest dispozitiv?</span>
                <span className="block text-sm text-muted-foreground">Acest dispozitiv va fi deconectat si va trebui sa va conectati din nou pentru a continua utilizarea acestui dispozitiv.</span>
              </>
            )}
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button
            variant="outline"
            onClick={() => setDeviceToDelete(null)}
            disabled={deletingId === deviceToDelete}
          >
            Anulati
          </Button>
          <Button
            variant={isCurrentDevice(deviceToDelete || '') ? "default" : "destructive"}
            onClick={handleDeleteDevice}
            disabled={deletingId === deviceToDelete}
          >
            {deletingId === deviceToDelete ? (
              <>
                <span className="mr-2">Se Sterge...</span>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              </>
            ) : (
              isCurrentDevice(deviceToDelete || '') ? 'Deconectare' : 'Sterge Dispozitiv'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {devices.length === 0 && (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🔒</div>
        <h3 className="text-lg font-medium text-gray-800 mb-2">Nu aveti niciun dispozitiv conectat</h3>
        <p className="text-gray-600">Nu exista dispozitive active momentan.</p>
      </div>
    )}
  </>
);
}