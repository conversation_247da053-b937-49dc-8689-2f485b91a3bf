"use client";

import * as React from "react";
import { useUser, useReverification } from "@clerk/nextjs";
import { BackupCodeResource, TOTPResource } from "@clerk/types";
import { QRCodeSVG } from "qrcode.react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Loader2,
  CheckCircle,
  AlertCircle,
  Smartphone,
  Shield,
  Copy,
  Download,
  AlertTriangle,
  QrCode,
  Key,
  RefreshCw
} from "lucide-react";

type MFAStep = "overview" | "setup" | "verify" | "backup-codes" | "success";
type DisplayFormat = "qr" | "uri";

interface MFADialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onMFAStatusChange?: (enabled: boolean) => void;
}

// Backup codes generation component
function BackupCodesDisplay({
  backupCodes,
  onRegenerate
}: {
  backupCodes: BackupCodeResource;
  onRegenerate?: () => void;
}) {
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = async () => {
    const codesText = backupCodes.codes.join('\n');
    await navigator.clipboard.writeText(codesText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadCodes = () => {
    const codesText = backupCodes.codes.join('\n');
    const blob = new Blob([codesText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'backup-codes.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-4">
      <Alert>
        <Key className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> Salvati aceste coduri de rezerva intr-un loc sigur.
          Le puteti folosi pentru a va accesa contul daca va pierdeti dispozitivul de autentificare.
        </AlertDescription>
      </Alert>

      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-2 gap-2 font-mono text-sm">
            {backupCodes.codes.map((code, index) => (
              <div key={index} className="p-2 bg-muted rounded border text-center">
                {code}
              </div>
            ))}
          </div>

          <div className="flex gap-2 mt-4">
            <Button variant="outline" size="sm" onClick={copyToClipboard}>
              {copied ? <CheckCircle className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
              {copied ? "Copiat!" : "Copiaza"}
            </Button>
            <Button variant="outline" size="sm" onClick={downloadCodes}>
              <Download className="h-4 w-4 mr-2" />
              Descarca
            </Button>
            {onRegenerate && (
              <Button variant="outline" size="sm" onClick={onRegenerate}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenereaza
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// TOTP Setup component
function TOTPSetup({
  onNext,
  onCancel
}: {
  onNext: (totp: TOTPResource) => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const [totp, setTOTP] = React.useState<TOTPResource | undefined>(undefined);
  const [displayFormat, setDisplayFormat] = React.useState<DisplayFormat>("qr");
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  const createTOTP = useReverification(() => user?.createTOTP());

  React.useEffect(() => {
    createTOTP()
      .then((totpResource: TOTPResource | undefined) => {
        if (totpResource) {
          setTOTP(totpResource);
        } else {
          setError("Nu am putut genera codul QR. Va rugam sa incercati din nou.");
        }
        setLoading(false);
      })
      .catch((err) => {
        console.error("Error creating TOTP:", err);
        setError("Nu am putut genera codul QR. Va rugam sa incercati din nou.");
        setLoading(false);
      });
  }, [createTOTP]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Se genereaza codul QR...</span>
      </div>
    );
  }

  if (error || !totp) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error || "A aparut o eroare neasteptata."}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <div className="mx-auto w-fit">
          {displayFormat === "qr" ? (
            <div className="p-4 bg-white rounded-lg border">
              <QRCodeSVG value={totp.uri || ""} size={200} />
            </div>
          ) : (
            <div className="p-4 bg-muted rounded-lg border break-all text-sm font-mono">
              {totp.uri}
            </div>
          )}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => setDisplayFormat(displayFormat === "qr" ? "uri" : "qr")}
        >
          {displayFormat === "qr" ? (
            <>
              <Key className="h-4 w-4 mr-2" />
              Afiseaza URI
            </>
          ) : (
            <>
              <QrCode className="h-4 w-4 mr-2" />
              Afiseaza QR Code
            </>
          )}
        </Button>
      </div>

      <div className="space-y-3">
        <h4 className="font-medium">Instructiuni:</h4>
        <ol className="list-decimal list-inside space-y-2 text-sm text-muted-foreground">
          <li>Instalati o aplicatie de autentificare (Google Authenticator, Authy, etc.)</li>
          <li>Scanati codul QR sau introduceti manual URI-ul</li>
          <li>Introduceti codul generat de aplicatie pentru verificare</li>
        </ol>
      </div>

      <div className="flex gap-2 justify-end">
        <Button variant="outline" onClick={onCancel}>
          Anuleaza
        </Button>
        <Button onClick={() => onNext(totp)}>
          Am configurat aplicatia
        </Button>
      </div>
    </div>
  );
}

// TOTP Verification component
function TOTPVerification({
  onSuccess,
  onCancel
}: {
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const { user } = useUser();
  const [code, setCode] = React.useState("");
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleVerify = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!code.trim()) return;

    setLoading(true);
    setError(null);

    try {
      await user?.verifyTOTP({ code: code.trim() });
      onSuccess();
    } catch (err: any) {
      console.error("Error verifying TOTP:", err);
      setError("Codul introdus este incorect. Va rugam sa incercati din nou.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Alert>
        <Smartphone className="h-4 w-4" />
        <AlertDescription>
          Introduceti codul de 6 cifre generat de aplicatia dvs. de autentificare.
        </AlertDescription>
      </Alert>

      <form onSubmit={handleVerify} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="totp-code">Cod de verificare</Label>
          <Input
            id="totp-code"
            type="text"
            placeholder="123456"
            value={code}
            onChange={(e) => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            maxLength={6}
            className="text-center text-lg font-mono tracking-widest"
            disabled={loading}
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2 justify-end">
          <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
            Anuleaza
          </Button>
          <Button type="submit" disabled={loading || code.length !== 6}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Se verifica...
              </>
            ) : (
              "Verifica codul"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

// Main MFA Dialog component
export default function MFADialog({ open, onOpenChange, onMFAStatusChange }: MFADialogProps) {
  const { isLoaded, user } = useUser();
  const [step, setStep] = React.useState<MFAStep>("overview");
  const [currentTOTP, setCurrentTOTP] = React.useState<TOTPResource | undefined>(undefined);
  const [backupCodes, setBackupCodes] = React.useState<BackupCodeResource | undefined>(undefined);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const disableTOTP = useReverification(() => user?.disableTOTP());
  const createBackupCode = useReverification(() => user?.createBackupCode());

  // Reset state when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setStep("overview");
      setCurrentTOTP(undefined);
      setBackupCodes(undefined);
      setError(null);
    }
  }, [open]);

  const handleDisableMFA = async () => {
    setLoading(true);
    setError(null);

    try {
      await disableTOTP();
      onMFAStatusChange?.(false);
      onOpenChange(false);
    } catch (err: any) {
      console.error("Error disabling MFA:", err);
      setError("Nu am putut dezactiva 2FA. Va rugam sa incercati din nou.");
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateBackupCodes = async () => {
    setLoading(true);
    setError(null);

    try {
      const codes = await createBackupCode();
      if (codes) {
        setBackupCodes(codes);
      } else {
        setError("Nu am putut genera codurile de rezerva.");
      }
    } catch (err: any) {
      console.error("Error generating backup codes:", err);
      setError("Nu am putut genera codurile de rezerva. Va rugam sa incercati din nou.");
    } finally {
      setLoading(false);
    }
  };

  const handleSetupSuccess = () => {
    setStep("backup-codes");
    handleGenerateBackupCodes();
  };

  const handleComplete = () => {
    onMFAStatusChange?.(true);
    onOpenChange(false);
  };

  if (!isLoaded) {
    return null;
  }

  const getDialogTitle = () => {
    switch (step) {
      case "setup": return "Configurare Autentificare cu Doi Factori";
      case "verify": return "Verificare Cod 2FA";
      case "backup-codes": return "Coduri de Rezerva";
      case "success": return "2FA Activat cu Succes!";
      default: return "Gestionare Autentificare cu Doi Factori";
    }
  };

  const getDialogDescription = () => {
    switch (step) {
      case "setup": return "Scanati codul QR cu aplicatia dvs. de autentificare";
      case "verify": return "Introduceti codul generat de aplicatia de autentificare";
      case "backup-codes": return "Salvati aceste coduri intr-un loc sigur";
      case "success": return "Contul dvs. este acum protejat cu 2FA";
      default: return "Configurati sau gestionati autentificarea cu doi factori";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>
            {getDialogDescription()}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {step === "overview" && (
            <div className="space-y-6">
              {user?.totpEnabled ? (
                <div className="space-y-4">
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>2FA este activat.</strong> Contul dvs. este protejat cu autentificare cu doi factori.
                    </AlertDescription>
                  </Alert>

                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={handleGenerateBackupCodes}
                      disabled={loading}
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Key className="h-4 w-4 mr-2" />
                      )}
                      Genereaza coduri de rezerva
                    </Button>
                    <Button
                      variant="destructive"
                      onClick={handleDisableMFA}
                      disabled={loading}
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 mr-2" />
                      )}
                      Dezactiveaza 2FA
                    </Button>
                  </div>

                  {backupCodes && (
                    <BackupCodesDisplay
                      backupCodes={backupCodes}
                      onRegenerate={handleGenerateBackupCodes}
                    />
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <Alert variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>2FA nu este activat.</strong> Activati autentificarea cu doi factori pentru a va proteja contul.
                    </AlertDescription>
                  </Alert>

                  <Button onClick={() => setStep("setup")} className="w-full">
                    <Shield className="h-4 w-4 mr-2" />
                    Activeaza 2FA
                  </Button>
                </div>
              )}
            </div>
          )}

          {step === "setup" && (
            <TOTPSetup
              onNext={(totp) => {
                setCurrentTOTP(totp);
                setStep("verify");
              }}
              onCancel={() => setStep("overview")}
            />
          )}

          {step === "verify" && currentTOTP && (
            <TOTPVerification
              onSuccess={handleSetupSuccess}
              onCancel={() => setStep("setup")}
            />
          )}

          {step === "backup-codes" && backupCodes && (
            <div className="space-y-4">
              <BackupCodesDisplay backupCodes={backupCodes} />
              <Button onClick={handleComplete} className="w-full">
                <CheckCircle className="h-4 w-4 mr-2" />
                Finalizeaza configurarea
              </Button>
            </div>
          )}

          {step === "success" && (
            <div className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold">2FA activat cu succes!</h3>
                <p className="text-sm text-muted-foreground">
                  Contul dvs. este acum protejat cu autentificare cu doi factori.
                </p>
              </div>
              <Button onClick={() => onOpenChange(false)} className="w-full">
                Inchide
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
