"server-only"

import { format<PERSON>riceRON } from "@/lib/utils";
import { Cart } from "@/types/cart";
import CartPageItem from "./CartPageItem";
import CartObservatiiRoute from "./CartObservatiiRoute";
import { CartButtonOrder } from "./CartButtonOrder";


const CartPage = ({ cart }: { cart: Cart  }) => {
  const subtotal = cart.items
    .filter(item => item.addToOrder)
    .reduce(
      (acc, item) => acc + (item.FinalPrice ?? 0) * item.quantity,
      0
    );
  const shipping = (subtotal > 200 || subtotal === 0) ? 0 : 20;
  const total = subtotal + shipping;

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold mb-8">
          Cosul tau ({cart.items.length} produse)
        </h1>

        <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
          {/* Cart Items */}
          <div className="w-full lg:w-2/3 border rounded-lg shadow p-6">
            {cart.items.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-6 p-6 border border-gray-200 last:border-0"
              >
                {/* Item Details*/}
                <CartPageItem item={item} />

                {/* Item Price */}
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    {formatPriceRON((item.FinalPrice ? item.FinalPrice : 0) * item.quantity)}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {formatPriceRON(item.FinalPrice)} buc
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="w-full lg:w-1/3 border rounded-lg shadow p-6 h-fit">
            <h2 className="text-xl font-semibold mb-6">
              Sumar comanda
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{ formatPriceRON(subtotal) }</span>
              </div>
              <div className="flex justify-between">
                <span>Taxa de livrare</span>
                <span>
                  {formatPriceRON(shipping)}
                </span>
              </div>{shipping > 0 && ( <p className="text-xs text-muted-foreground">*Transport gratuit pentru comenzi peste 200 RON</p> )}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatPriceRON(total)}</span>
                </div>
              </div>
              {/*textarea for notes*/}
              <CartObservatiiRoute cart={cart} />
            </div>
              <CartButtonOrder />
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default CartPage;
