"use client"

import React  from "react";
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const SearchSection = ({
  onSearch = () => {},
  onPartNumberLookup = () => {},
  query,
}: { query?: string ,
  onSearch?: (searchTerm: string) => void;
  onPartNumberLookup?: (partNumber: string) => void;
}) => {

  return (
    <div
      className="w-full max-w-2xl mx-auto p-2 md:p-4"
    >
      <form
        className="flex flex-wrap md:flex-nowrap gap-2 relative"
      >
        <div className="relative flex-1">
          <Input
            type="text"
            value={query}
            onChange={(e) => onSearch(e.target.value)}
            placeholder="Cauta produse dupa nume sau cod OE..."
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                onSearch(e.currentTarget.value);
              }
            }}
            className="w-full pl-10 pr-4 h-12 text-base"
          />
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={20}
          />
        </div>

        <Button
          type="submit"
          className="h-12 px-6 bg-[#0066B1] hover:bg-[#004d85] text-white"
        >
          Cauta
        </Button>
      </form>
    </div>
  );
};

export default SearchSection;

