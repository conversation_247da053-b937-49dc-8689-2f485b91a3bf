"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils";
import { SearchSuggestionsResult } from "@/types/search";



// Get search suggestions (max 5 products + first relevant category)
export async function getSearchSuggestions(query: string): Promise<SearchSuggestionsResult> {
  console.log("getSearchSuggestions called");
  if (!query || query.length < 3) {
    return { products: [], firstCategory: null }
  }

  try {
    // Search for products
    const products = await withRetry(() => prisma.product.findMany({
      where: {
        OR: [
          { Material_Number: { contains: query.toLowerCase() } },
          { Description_Local: { contains: query.toLowerCase() } }
        ],
        isActive: true
      },
      take: 5,
      select: {
        Material_Number: true,
        Description_Local: true,
        ImageUrl: true,
        FinalPrice: true,
        PretAM: true,
        HasDiscount: true,
        activeDiscountType: true,
        activeDiscountValue: true,
        discountPercentage: true,
        categoryLevel3: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      },
      orderBy: [
        { HasDiscount: 'desc' }, // Prioritize discounted products
        { FinalPrice: 'asc' }
      ]
    }))

    // Find first relevant category from the products found
    const firstCategory = products.length > 0 && products[0].categoryLevel3
      ? {
          id: products[0].categoryLevel3.id,
          name: products[0].categoryLevel3.name,
          slug: products[0].categoryLevel3.slug
        }
      : null

    // Remove categoryLevel3 from products for cleaner response
    const cleanProducts = products.map(({ categoryLevel3, ...product }) => product)

    //convert cleanProducts to SearchSuggestionsResult interface, example :Decimal to number
    const searchSuggestionsResult = cleanProducts.map((product) => ({
      Material_Number: product.Material_Number,
      Description_Local: product.Description_Local,
      ImageUrl: product.ImageUrl,
      FinalPrice: toSafeNumber(product.FinalPrice) ,
      PretAM: toSafeNumber(product.PretAM),
      HasDiscount: product.HasDiscount,
      activeDiscountType: product.activeDiscountType,
      activeDiscountValue:  toSafeNumber(product.activeDiscountValue),
      discountPercentage:  toSafeNumber(product.discountPercentage)
    }))

    return {
      products: searchSuggestionsResult,
      firstCategory
    }

  } catch (error) {
    logger.error('Error fetching search suggestions:', error)
    return { products: [], firstCategory: null }
  }
}