
import type { DiscountType as PrismaDiscountType } from "@/generated/prisma";

export interface SearchSuggestion {
  Material_Number: string
  Description_Local: string | null
  ImageUrl: string[]
  FinalPrice: number | null
  PretAM: number | null
  HasDiscount: boolean
  discountPercentage: number | null
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
}

export interface SearchSuggestionsResult {
  products: SearchSuggestion[]
  firstCategory: {
    id: string
    name: string
    slug: string | null
  } | null
}